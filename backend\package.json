{"name": "backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "description": "", "dependencies": {"@neondatabase/serverless": "^1.0.1", "cors": "^2.8.5", "cron": "^4.3.1", "dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "express": "^5.1.0"}, "devDependencies": {"drizzle-kit": "^0.31.1", "nodemon": "^3.1.10"}}