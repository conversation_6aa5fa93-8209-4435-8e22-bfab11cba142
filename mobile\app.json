{"expo": {"name": "Recipe App", "slug": "recipe-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "recipe-app", "userInterfaceStyle": "automatic", "newArchEnabled": true, "privacy": "public", "platforms": ["ios", "android"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.recipeapp.mobile", "buildNumber": "1"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.recipeapp.mobile", "versionCode": 1}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}