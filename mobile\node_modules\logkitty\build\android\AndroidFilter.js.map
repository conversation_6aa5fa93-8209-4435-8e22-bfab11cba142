{"version": 3, "sources": ["../../src/android/AndroidFilter.ts"], "names": ["AndroidFilter", "constructor", "minPriority", "filter", "entry", "priority", "setFilterByTag", "tags", "Boolean", "tag", "indexOf", "setFilterByApp", "applicationId", "adbPath", "pid", "setFilterByMatch", "regexes", "find", "reg", "messages", "message", "test", "set<PERSON>ustom<PERSON>ilter", "patterns", "tagFilters", "reduce", "acc", "pattern", "split", "Priority", "fromLetter", "SILENT", "UNKNOWN", "shouldInclude"], "mappings": ";;;;;;;AACA;;AACA;;;;AAIe,MAAMA,aAAN,CAAuC;AAIpDC,EAAAA,WAAW,CAACC,WAAmB,GAAG,CAAvB,EAA0B;AAAA;;AAAA;;AACnC,SAAKA,WAAL,GAAmBA,WAAnB,CADmC,CAEnC;;AACA,SAAKC,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aAAOA,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAA9B;AACD,KAFD;AAGD;;AAEDI,EAAAA,cAAc,CAACC,IAAD,EAAiB;AAC7B,SAAKJ,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aAAOI,OAAO,CACZJ,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAAvB,IACEE,KAAK,CAACK,GADR,IAEEF,IAAI,CAACG,OAAL,CAAaN,KAAK,CAACK,GAAnB,IAA0B,CAAC,CAHjB,CAAd;AAKD,KAND;AAOD;;AAEDE,EAAAA,cAAc,CAACC,aAAD,EAAwBC,OAAxB,EAA0C;AACtD,UAAMC,GAAG,GAAG,4BAAkBF,aAAlB,EAAiCC,OAAjC,CAAZ;;AACA,SAAKV,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aAAOA,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAAvB,IAAsCE,KAAK,CAACU,GAAN,KAAcA,GAA3D;AACD,KAFD;AAGD;;AAEDC,EAAAA,gBAAgB,CAACC,OAAD,EAAoB;AAClC,SAAKb,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aACEA,KAAK,CAACC,QAAN,IAAkB,KAAKH,WAAvB,IACAM,OAAO,CACLQ,OAAO,CAACC,IAAR,CAAcC,GAAD,IACXV,OAAO,CAACJ,KAAK,CAACe,QAAN,CAAeF,IAAf,CAAqBG,OAAD,IAAqBF,GAAG,CAACG,IAAJ,CAASD,OAAT,CAAzC,CAAD,CADT,CADK,CAFT;AAQD,KATD;AAUD;;AAEDE,EAAAA,eAAe,CAACC,QAAD,EAAqB;AAClC,UAAMC,UAAqC,GAAGD,QAAQ,CAACE,MAAT,CAC5C,CAACC,GAAD,EAAiCC,OAAjC,KAAqD;AACnD,YAAM,CAAClB,GAAD,EAAMJ,QAAN,IAAkBsB,OAAO,CAACC,KAAR,CAAc,GAAd,CAAxB;AACA,aAAO,EACL,GAAGF,GADE;AAEL,SAACjB,GAAD,GAAOoB,oBAASC,UAAT,CAAoBzB,QAApB;AAFF,OAAP;AAID,KAP2C,EAQ5C,EAR4C,CAA9C;;AAUA,SAAKF,MAAL,GAAeC,KAAD,IAAkB;AAC9B,aACGA,KAAK,CAACK,GAAN,IACCL,KAAK,CAACC,QAAN,KAAmBmB,UAAU,CAACpB,KAAK,CAACK,GAAP,CAAV,IAAyBoB,oBAASE,MAArD,CADF,IAEA3B,KAAK,CAACC,QAAN,KAAmBmB,UAAU,CAAC,GAAD,CAAV,IAAmBK,oBAASG,OAA/C,CAHF;AAKD,KAND;AAOD;;AAEDC,EAAAA,aAAa,CAAC7B,KAAD,EAAe;AAC1B,WAAO,KAAKD,MAAL,CAAYC,KAAZ,CAAP;AACD;;AAhEmD", "sourcesContent": ["import { IFilter, Entry } from '../types';\nimport { getApplicationPid } from './adb';\nimport { Priority } from './constants';\n\ntype Filter = (entry: Entry) => boolean;\n\nexport default class AndroidFilter implements IFilter {\n  private readonly minPriority: number;\n  private filter: Filter;\n\n  constructor(minPriority: number = 0) {\n    this.minPriority = minPriority;\n    // Default filter by all\n    this.filter = (entry: Entry) => {\n      return entry.priority >= this.minPriority;\n    };\n  }\n\n  setFilterByTag(tags: string[]) {\n    this.filter = (entry: Entry) => {\n      return Boolean(\n        entry.priority >= this.minPriority &&\n          entry.tag &&\n          tags.indexOf(entry.tag) > -1\n      );\n    };\n  }\n\n  setFilterByApp(applicationId: string, adbPath?: string) {\n    const pid = getApplicationPid(applicationId, adbPath);\n    this.filter = (entry: Entry) => {\n      return entry.priority >= this.minPriority && entry.pid === pid;\n    };\n  }\n\n  setFilterByMatch(regexes: RegExp[]) {\n    this.filter = (entry: Entry) => {\n      return (\n        entry.priority >= this.minPriority &&\n        Boolean(\n          regexes.find((reg: RegExp) =>\n            Boolean(entry.messages.find((message: string) => reg.test(message)))\n          )\n        )\n      );\n    };\n  }\n\n  setCustomFilter(patterns: string[]) {\n    const tagFilters: { [key: string]: number } = patterns.reduce(\n      (acc: { [key: string]: number }, pattern: string) => {\n        const [tag, priority] = pattern.split(':');\n        return {\n          ...acc,\n          [tag]: Priority.fromLetter(priority),\n        };\n      },\n      {}\n    );\n    this.filter = (entry: Entry) => {\n      return (\n        (entry.tag &&\n          entry.priority >= (tagFilters[entry.tag] || Priority.SILENT)) ||\n        entry.priority >= (tagFilters['*'] || Priority.UNKNOWN)\n      );\n    };\n  }\n\n  shouldInclude(entry: Entry) {\n    return this.filter(entry);\n  }\n}\n"], "file": "AndroidFilter.js"}