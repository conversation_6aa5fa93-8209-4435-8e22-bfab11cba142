{"version": 3, "sources": ["../src/api.ts"], "names": ["makeTagsFilter", "tags", "platform", "minPriority", "filter", "AndroidFilter", "<PERSON>os<PERSON><PERSON><PERSON>", "setFilterByTag", "makeAppFilter", "appIdentifier", "adbPath", "Error", "setFilterByApp", "makeMatchFilter", "regexes", "setFilterByMatch", "makeCustomFilter", "patterns", "set<PERSON>ustom<PERSON>ilter", "<PERSON><PERSON><PERSON>", "options", "priority", "createFilter", "emitter", "EventEmitter", "some", "availablePlatform", "parser", "AndroidParser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loggingProcess", "process", "on", "kill", "emit", "stderr", "errorData", "toString", "includes", "CodeError", "ERR_IOS_NO_SIMULATORS_BOOTED", "stdout", "raw", "entryToLog", "messages", "splitMessages", "entries", "parseMessages", "for<PERSON>ach", "entry", "shouldInclude", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AACA;;AAGA;;AACA;;AACA;;AACA;;AAGA;;AACA;;AACA;;AACA;;AACA;;AAGA;;;;AAlBA;;AAIA;;AAMA;;AAOA;AAiBO,SAASA,cAAT,CAAwB,GAAGC,IAA3B,EAA0D;AAC/D,SAAO,CAACC,QAAD,EAAqBC,WAArB,KAA8C;AACnD,UAAMC,MAAM,GACVF,QAAQ,KAAK,SAAb,GACI,IAAIG,sBAAJ,CAAkBF,WAAlB,CADJ,GAEI,IAAIG,kBAAJ,CAAcH,WAAd,CAHN;AAIAC,IAAAA,MAAM,CAACG,cAAP,CAAsBN,IAAtB;AACA,WAAOG,MAAP;AACD,GAPD;AAQD;;AAEM,SAASI,aAAT,CAAuBC,aAAvB,EAA6D;AAClE,SAAO,CAACP,QAAD,EAAqBC,WAArB,EAA2CO,OAA3C,KAAgE;AACrE,QAAIR,QAAQ,KAAK,SAAjB,EAA4B;AAC1B,YAAM,IAAIS,KAAJ,CAAU,0CAAV,CAAN;AACD;;AAED,UAAMP,MAAM,GAAG,IAAIC,sBAAJ,CAAkBF,WAAlB,CAAf;AACAC,IAAAA,MAAM,CAACQ,cAAP,CAAsBH,aAAtB,EAAqCC,OAArC;AACA,WAAON,MAAP;AACD,GARD;AASD;;AAEM,SAASS,eAAT,CAAyB,GAAGC,OAA5B,EAA8D;AACnE,SAAO,CAACZ,QAAD,EAAqBC,WAArB,KAA8C;AACnD,UAAMC,MAAM,GACVF,QAAQ,KAAK,SAAb,GACI,IAAIG,sBAAJ,CAAkBF,WAAlB,CADJ,GAEI,IAAIG,kBAAJ,CAAcH,WAAd,CAHN;AAIAC,IAAAA,MAAM,CAACW,gBAAP,CAAwBD,OAAxB;AACA,WAAOV,MAAP;AACD,GAPD;AAQD;;AAEM,SAASY,gBAAT,CAA0B,GAAGC,QAA7B,EAAgE;AACrE,SAAO,CAACf,QAAD,EAAqBC,WAArB,KAA8C;AACnD,QAAID,QAAQ,KAAK,SAAjB,EAA4B;AAC1B,YAAM,IAAIS,KAAJ,CAAU,6CAAV,CAAN;AACD;;AAED,UAAMP,MAAM,GAAG,IAAIC,sBAAJ,CAAkBF,WAAlB,CAAf;AACAC,IAAAA,MAAM,CAACc,eAAP,CAAuBD,QAAvB;AACA,WAAOb,MAAP;AACD,GARD;AASD;;AAEM,SAASe,QAAT,CAAkBC,OAAlB,EAA0D;AAC/D,QAAM;AAAElB,IAAAA,QAAF;AAAYQ,IAAAA,OAAZ;AAAqBW,IAAAA,QAArB;AAA+BjB,IAAAA,MAAM,EAAEkB;AAAvC,MAAwDF,OAA9D;AACA,QAAMG,OAAO,GAAG,IAAIC,oBAAJ,EAAhB;;AAEA,MACE,CAAC,CAAC,KAAD,EAAQ,SAAR,EAAmBC,IAAnB,CACCC,iBAAiB,IAAIA,iBAAiB,KAAKxB,QAD5C,CADH,EAIE;AACA,UAAM,IAAIS,KAAJ,CAAW,YAAWT,QAAS,mBAA/B,CAAN;AACD;;AAED,QAAMyB,MAAM,GAAGzB,QAAQ,KAAK,SAAb,GAAyB,IAAI0B,sBAAJ,EAAzB,GAA+C,IAAIC,kBAAJ,EAA9D;AACA,MAAIzB,MAAJ;;AACA,MAAIkB,YAAJ,EAAkB;AAChBlB,IAAAA,MAAM,GAAGkB,YAAY,CAACpB,QAAD,EAAWmB,QAAX,EAAqBX,OAArB,CAArB;AACD,GAFD,MAEO;AACLN,IAAAA,MAAM,GACJF,QAAQ,KAAK,SAAb,GACI,IAAIG,sBAAJ,CAAkBgB,QAAlB,CADJ,GAEI,IAAIf,kBAAJ,CAAce,QAAd,CAHN;AAID;;AAED,QAAMS,cAAc,GAClB5B,QAAQ,KAAK,SAAb,GACI,mCAAyBQ,OAAzB,CADJ,GAEI,4CAHN;AAKAqB,EAAAA,OAAO,CAACC,EAAR,CAAW,MAAX,EAAmB,MAAM;AACvBF,IAAAA,cAAc,CAACG,IAAf;AACAV,IAAAA,OAAO,CAACW,IAAR,CAAa,MAAb;AACD,GAHD;AAKAJ,EAAAA,cAAc,CAACK,MAAf,CAAsBH,EAAtB,CAAyB,MAAzB,EAAkCI,SAAD,IAAgC;AAC/D,QACElC,QAAQ,KAAK,KAAb,IACAkC,SAAS,CAACC,QAAV,GAAqBC,QAArB,CAA8B,wBAA9B,CAFF,EAGE;AACAf,MAAAA,OAAO,CAACW,IAAR,CACE,OADF,EAEE,IAAIK,iBAAJ,CAAcC,oCAAd,EAA4C,2BAA5C,CAFF;AAID,KARD,MAQO;AACLjB,MAAAA,OAAO,CAACW,IAAR,CAAa,OAAb,EAAsB,IAAIvB,KAAJ,CAAUyB,SAAS,CAACC,QAAV,EAAV,CAAtB;AACD;AACF,GAZD;AAcAP,EAAAA,cAAc,CAACW,MAAf,CAAsBT,EAAtB,CAAyB,MAAzB,EAAkCU,GAAD,IAA0B;AACzD,QAAIC,UAAJ;;AACA,QAAI;AACF,YAAMC,QAAQ,GAAGjB,MAAM,CAACkB,aAAP,CAAqBH,GAAG,CAACL,QAAJ,EAArB,CAAjB;AACA,YAAMS,OAAO,GAAGnB,MAAM,CAACoB,aAAP,CAAqBH,QAArB,CAAhB;AACAE,MAAAA,OAAO,CAACE,OAAR,CAAiBC,KAAD,IAAkB;AAChC,YAAI7C,MAAM,CAAC8C,aAAP,CAAqBD,KAArB,CAAJ,EAAiC;AAC/BN,UAAAA,UAAU,GAAGM,KAAb;AACD;AACF,OAJD;AAKD,KARD,CAQE,OAAOE,KAAP,EAAc;AACd5B,MAAAA,OAAO,CAACW,IAAR,CAAa,OAAb,EAAsBiB,KAAtB;AACD;;AAED,QAAIR,UAAJ,EAAgB;AACdpB,MAAAA,OAAO,CAACW,IAAR,CAAa,OAAb,EAAsBS,UAAtB;AACD;AACF,GAjBD;AAmBAb,EAAAA,cAAc,CAACW,MAAf,CAAsBT,EAAtB,CAAyB,OAAzB,EAAmCmB,KAAD,IAAkB;AAClD5B,IAAAA,OAAO,CAACW,IAAR,CAAa,OAAb,EAAsBiB,KAAtB;AACA5B,IAAAA,OAAO,CAACW,IAAR,CAAa,MAAb;AACD,GAHD;AAKA,SAAOX,OAAP;AACD", "sourcesContent": ["/* Common */\nimport { EventEmitter } from 'events';\nimport { IFilter, Entry, Platform } from './types';\n\n/* Android */\nimport AndroidFilter from './android/AndroidFilter';\nimport AndroidParser from './android/AndroidParser';\nimport { runAndroidLoggingProcess } from './android/adb';\nexport { Priority as AndroidPriority } from './android/constants';\n\n/* iOS */\nimport IosParser from './ios/IosParser';\nimport IosFilter from './ios/IosFilter';\nimport { runSimulatorLoggingProcess } from './ios/simulator';\nimport { CodeError, ERR_IOS_NO_SIMULATORS_BOOTED } from './errors';\nexport { Priority as IosPriority } from './ios/constants';\n\n/* Exports */\nexport { formatEntry, formatError } from './formatters';\nexport { Entry } from './types';\n\nexport type LogkittyOptions = {\n  platform: Platform;\n  adbPath?: string;\n  priority?: number;\n  filter?: FilterCreator;\n};\n\nexport type FilterCreator = (\n  platform: Platform,\n  minPriority?: number,\n  adbPath?: string\n) => IFilter;\n\nexport function makeTagsFilter(...tags: string[]): FilterCreator {\n  return (platform: Platform, minPriority?: number) => {\n    const filter =\n      platform === 'android'\n        ? new AndroidFilter(minPriority)\n        : new IosFilter(minPriority);\n    filter.setFilterByTag(tags);\n    return filter;\n  };\n}\n\nexport function makeAppFilter(appIdentifier: string): FilterCreator {\n  return (platform: Platform, minPriority?: number, adbPath?: string) => {\n    if (platform !== 'android') {\n      throw new Error('App filter is only available for Android');\n    }\n\n    const filter = new AndroidFilter(minPriority);\n    filter.setFilterByApp(appIdentifier, adbPath);\n    return filter;\n  };\n}\n\nexport function makeMatchFilter(...regexes: RegExp[]): FilterCreator {\n  return (platform: Platform, minPriority?: number) => {\n    const filter =\n      platform === 'android'\n        ? new AndroidFilter(minPriority)\n        : new IosFilter(minPriority);\n    filter.setFilterByMatch(regexes);\n    return filter;\n  };\n}\n\nexport function makeCustomFilter(...patterns: string[]): FilterCreator {\n  return (platform: Platform, minPriority?: number) => {\n    if (platform !== 'android') {\n      throw new Error('Custom filter is only available for Android');\n    }\n\n    const filter = new AndroidFilter(minPriority);\n    filter.setCustomFilter(patterns);\n    return filter;\n  };\n}\n\nexport function logkitty(options: LogkittyOptions): EventEmitter {\n  const { platform, adbPath, priority, filter: createFilter } = options;\n  const emitter = new EventEmitter();\n\n  if (\n    !['ios', 'android'].some(\n      availablePlatform => availablePlatform === platform\n    )\n  ) {\n    throw new Error(`Platform ${platform} is not supported`);\n  }\n\n  const parser = platform === 'android' ? new AndroidParser() : new IosParser();\n  let filter: IFilter;\n  if (createFilter) {\n    filter = createFilter(platform, priority, adbPath);\n  } else {\n    filter =\n      platform === 'android'\n        ? new AndroidFilter(priority)\n        : new IosFilter(priority);\n  }\n\n  const loggingProcess =\n    platform === 'android'\n      ? runAndroidLoggingProcess(adbPath)\n      : runSimulatorLoggingProcess();\n\n  process.on('exit', () => {\n    loggingProcess.kill();\n    emitter.emit('exit');\n  });\n\n  loggingProcess.stderr.on('data', (errorData: string | Buffer) => {\n    if (\n      platform === 'ios' &&\n      errorData.toString().includes('No devices are booted.')\n    ) {\n      emitter.emit(\n        'error',\n        new CodeError(ERR_IOS_NO_SIMULATORS_BOOTED, 'No simulators are booted.')\n      );\n    } else {\n      emitter.emit('error', new Error(errorData.toString()));\n    }\n  });\n\n  loggingProcess.stdout.on('data', (raw: string | Buffer) => {\n    let entryToLog: Entry | undefined;\n    try {\n      const messages = parser.splitMessages(raw.toString());\n      const entries = parser.parseMessages(messages);\n      entries.forEach((entry: Entry) => {\n        if (filter.shouldInclude(entry)) {\n          entryToLog = entry;\n        }\n      });\n    } catch (error) {\n      emitter.emit('error', error);\n    }\n\n    if (entryToLog) {\n      emitter.emit('entry', entryToLog);\n    }\n  });\n\n  loggingProcess.stdout.on('error', (error: Error) => {\n    emitter.emit('error', error);\n    emitter.emit('exit');\n  });\n\n  return emitter;\n}\n"], "file": "api.js"}