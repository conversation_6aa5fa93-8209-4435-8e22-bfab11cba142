{"version": 3, "sources": ["../src/formatters.ts"], "names": ["formatError", "error", "code", "message", "build", "formatEntry", "entry", "priorityColor", "priorityModifier", "platform", "priority", "AndroidPriority", "ERROR", "IosPriority", "WARN", "VERBOSE", "DEBUG", "output", "date", "format", "toLetter", "tag", "appId", "messages", "length", "slice", "map", "line", "index", "arr"], "mappings": ";;;;;;;;AAAA;;AAUA;;AACA;;AAGO,SAASA,WAAT,CAAqBC,KAArB,EAAuD;AAC5D,SAAO,8BACL,0BAAM,KAAN,EAAa,8BAAb,CADK,EAEL,wBAAI,CAAJ,EAAO,IAAP,CAFK,EAGL,0BAAM,KAAN,EAAa,6BAAS,MAAT,EAAiB,MAAjB,CAAb,EAAuC,MAAvC,CAHK,EAIL,UAAUA,KAAV,GAAkBA,KAAK,CAACC,IAAxB,GAA+B,aAJ1B,EAKL,wBAAI,CAAJ,EAAO,IAAP,CALK,EAML,0BAAM,KAAN,EAAa,6BAAS,MAAT,EAAiB,SAAjB,CAAb,EAA0C,MAA1C,CANK,EAOLD,KAAK,CAACE,OAPD,EAQLC,KARK,EAAP;AASD;;AAEM,SAASC,WAAT,CAAqBC,KAArB,EAA2C;AAChD,MAAIC,aAAwB,GAAG,MAA/B;AACA,MAAIC,gBAA8B,GAAG,MAArC;;AAEA,MACGF,KAAK,CAACG,QAAN,KAAmB,SAAnB,IAAgCH,KAAK,CAACI,QAAN,IAAkBC,oBAAgBC,KAAnE,IACCN,KAAK,CAACG,QAAN,KAAmB,KAAnB,IAA4BH,KAAK,CAACI,QAAN,IAAkBG,qBAAYD,KAF7D,EAGE;AACAL,IAAAA,aAAa,GAAG,KAAhB;AACD,GALD,MAKO,IACLD,KAAK,CAACG,QAAN,KAAmB,SAAnB,IACAH,KAAK,CAACI,QAAN,KAAmBC,oBAAgBG,IAF9B,EAGL;AACAP,IAAAA,aAAa,GAAG,QAAhB;AACD,GALM,MAKA,IACJD,KAAK,CAACG,QAAN,KAAmB,SAAnB,IACCH,KAAK,CAACI,QAAN,KAAmBC,oBAAgBI,OADrC,IAECT,KAAK,CAACG,QAAN,KAAmB,KAAnB,IAA4BH,KAAK,CAACI,QAAN,KAAmBG,qBAAYG,KAHvD,EAIL;AACAR,IAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,QAAMS,MAAM,GAAG,8BACb,6BAAS,KAAT,EAAiB,IAAGX,KAAK,CAACY,IAAN,CAAWC,MAAX,CAAkB,UAAlB,CAA8B,GAAlD,CADa,EAEb,wBAAI,CAAJ,CAFa,EAGb,0BACEZ,aADF,EAEE,6BACEC,gBADF,EAEG,GACCF,KAAK,CAACG,QAAN,KAAmB,SAAnB,GACIE,oBAAgBS,QAAhB,CAAyBd,KAAK,CAACI,QAA/B,CADJ,GAEIG,qBAAYO,QAAZ,CAAqBd,KAAK,CAACI,QAA3B,CACL,IANH,CAFF,CAHa,EAcb,wBAAI,CAAJ,CAda,EAeb,6BACE,MADF,EAEE,0BACEH,aADF,EAEE,6BAASC,gBAAT,EAA2BF,KAAK,CAACe,GAAN,IAAaf,KAAK,CAACgB,KAAnB,IAA4B,EAAvD,CAFF,CAFF,CAfa,EAsBb,wBAAI,CAAJ,CAtBa,EAuBb,0BAAMf,aAAN,EAAqB,6BAASC,gBAAT,EAA2B,IAA3B,CAArB,CAvBa,EAwBb,wBAAI,CAAJ,CAxBa,EAyBb,0BAAMD,aAAN,EAAqB,6BAASC,gBAAT,EAA2BF,KAAK,CAACiB,QAAN,CAAe,CAAf,CAA3B,CAArB,CAzBa,EA0Bb,2BACEjB,KAAK,CAACiB,QAAN,CAAeC,MAAf,GAAwB,CAD1B,EAEE,8BACE,GAAGlB,KAAK,CAACiB,QAAN,CACAE,KADA,CACM,CADN,EAEAC,GAFA,CAEI,CAACC,IAAD,EAAeC,KAAf,EAA8BC,GAA9B,KACH,8BACE,wBAAI,CAAJ,EAAO,IAAP,CADF,EAEE,wBAAI,CAACvB,KAAK,CAACe,GAAN,IAAaf,KAAK,CAACgB,KAAnB,IAA4B,EAA7B,EAAiCE,MAAjC,GAA0C,EAA9C,CAFF,EAGE,0BACEjB,aADF,EAEE,6BACEA,aAAa,KAAK,MAAlB,GAA2B,KAA3B,GAAmC,MADrC,EAEG,GAAEqB,KAAK,KAAKC,GAAG,CAACL,MAAJ,GAAa,CAAvB,GAA2B,GAA3B,GAAiC,GAAI,GAF1C,CAFF,CAHF,EAUE,0BAAMjB,aAAN,EAAqB,6BAASC,gBAAT,EAA2BmB,IAA3B,CAArB,CAVF,CAHD,CADL,CAFF,EAoBE,EApBF,CA1Ba,EAgDbvB,KAhDa,EAAf;AAkDA,SAAQ,GAAEa,MAAO,IAAjB;AACD", "sourcesContent": ["import {\n  container,\n  color,\n  modifier,\n  pad,\n  AnsiColor,\n  AnsiModifier,\n  ifElse,\n} from 'ansi-fragments';\nimport { CodeError } from './errors';\nimport { Priority as AndroidPriority } from './android/constants';\nimport { Priority as IosPriority } from './ios/constants';\nimport { Entry } from './types';\n\nexport function formatError(error: CodeError | Error): string {\n  return container(\n    color('red', '✖︎ Ups, something went wrong'),\n    pad(2, '\\n'),\n    color('red', modifier('bold', 'CODE'), ' ▶︎ '),\n    'code' in error ? error.code : 'ERR_UNKNOWN',\n    pad(1, '\\n'),\n    color('red', modifier('bold', 'MESSAGE'), ' ▶︎ '),\n    error.message\n  ).build();\n}\n\nexport function formatEntry(entry: Entry): string {\n  let priorityColor: AnsiColor = 'none';\n  let priorityModifier: AnsiModifier = 'none';\n\n  if (\n    (entry.platform === 'android' && entry.priority >= AndroidPriority.ERROR) ||\n    (entry.platform === 'ios' && entry.priority >= IosPriority.ERROR)\n  ) {\n    priorityColor = 'red';\n  } else if (\n    entry.platform === 'android' &&\n    entry.priority === AndroidPriority.WARN\n  ) {\n    priorityColor = 'yellow';\n  } else if (\n    (entry.platform === 'android' &&\n      entry.priority === AndroidPriority.VERBOSE) ||\n    (entry.platform === 'ios' && entry.priority === IosPriority.DEBUG)\n  ) {\n    priorityModifier = 'dim';\n  }\n\n  const output = container(\n    modifier('dim', `[${entry.date.format('HH:mm:ss')}]`),\n    pad(1),\n    color(\n      priorityColor,\n      modifier(\n        priorityModifier,\n        `${\n          entry.platform === 'android'\n            ? AndroidPriority.toLetter(entry.priority)\n            : IosPriority.toLetter(entry.priority)\n        } |`\n      )\n    ),\n    pad(1),\n    modifier(\n      'bold',\n      color(\n        priorityColor,\n        modifier(priorityModifier, entry.tag || entry.appId || '')\n      )\n    ),\n    pad(1),\n    color(priorityColor, modifier(priorityModifier, '▶︎')),\n    pad(1),\n    color(priorityColor, modifier(priorityModifier, entry.messages[0])),\n    ifElse(\n      entry.messages.length > 1,\n      container(\n        ...entry.messages\n          .slice(1)\n          .map((line: string, index: number, arr: string[]) =>\n            container(\n              pad(1, '\\n'),\n              pad((entry.tag || entry.appId || '').length + 16),\n              color(\n                priorityColor,\n                modifier(\n                  priorityColor === 'none' ? 'dim' : 'none',\n                  `${index === arr.length - 1 ? '└' : '│'} `\n                )\n              ),\n              color(priorityColor, modifier(priorityModifier, line))\n            )\n          )\n      ),\n      ''\n    )\n  ).build();\n\n  return `${output}\\n`;\n}\n"], "file": "formatters.js"}