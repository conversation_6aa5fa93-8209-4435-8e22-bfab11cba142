{"name": "nocache", "author": "<PERSON> <<EMAIL>> (https://evilpacket.net)", "contributors": ["<PERSON> <<EMAIL>> (https://evanhahn.com)"], "description": "Middleware to destroy caching", "version": "3.0.4", "license": "MIT", "keywords": ["express", "connect", "nocache", "caching", "cache"], "homepage": "https://github.com/helmetjs/nocache", "repository": {"type": "git", "url": "git://github.com/helmetjs/nocache.git"}, "bugs": {"url": "https://github.com/helmetjs/nocache/issues", "email": "<EMAIL>"}, "engines": {"node": ">=12.0.0"}, "main": "index.js", "typings": "index.d.ts", "scripts": {"pretest": "npm run lint", "lint": "npm run lint:eslint && npm run lint:prettier", "lint:eslint": "eslint .", "lint:prettier": "prettier --check .", "format": "prettier --write .", "test": "node test"}, "devDependencies": {"connect": "^3.7.0", "eslint": "^8.16.0", "prettier": "^2.6.2", "supertest": "^6.2.3"}}